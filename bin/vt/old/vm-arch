#!/bin/bash

set -euo pipefail

VM_NAME="arch"

VCPUS=4
RAM_MB=4096
DISK_SIZE="40G"
WORKDIR="/var/lib/libvirt/images/${VM_NAME}-vm"
CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
BRIDGE_IF="virbr0"

ARCH_IMG_URL="https://geo.mirror.pkgbuild.com/images/latest/Arch-Linux-x86_64-cloudimg.qcow2"
BASE_IMG="${WORKDIR}/arch-latest.qcow2"
USERNAME="arch"
DEFAULT_PASSWORD="$VM_NAME"

if [[ -f "${HOME}/.ssh/id_ed25519.pub" ]]; then
    SSH_KEY="${HOME}/.ssh/id_ed25519.pub"
elif [[ -f "${HOME}/.ssh/id_rsa.pub" ]]; then
    SSH_KEY="${HOME}/.ssh/id_rsa.pub"
else
    ssh-keygen -t rsa -b 4096 -f "${HOME}/.ssh/id_rsa" -N ""
    SSH_KEY="${HOME}/.ssh/id_rsa.pub"
fi

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() { echo -e "${BLUE}[INFO]${NC} $*"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $*"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $*"; }
log_error() { echo -e "${RED}[ERROR]${NC} $*" >&2; }

PASSWORD_HASH=$(openssl passwd -6 "$DEFAULT_PASSWORD")
log_info "Generated password hash for user '$USERNAME'"

cleanup_on_error() {
    log_warn "Cleaning up due to error..."
    sudo rm -f "$DISK_IMG" 2>/dev/null || true
    sudo rm -f "$SEED_ISO" 2>/dev/null || true
    sudo virsh destroy "$VM_NAME" 2>/dev/null || true
    sudo virsh undefine "$VM_NAME" 2>/dev/null || true

    stop_sudo_keepalive
}

trap cleanup_on_error ERR

usage() {
    cat <<EOF
Usage: $0 [OPTIONS]

Create an Arch Linux VM using virt-install and cloud-init.

OPTIONS:
    --name NAME         VM name (default: $VM_NAME)
    --memory MB         RAM in MB (default: $RAM_MB)
    --vcpus NUM         Number of vCPUs (default: $VCPUS)
    --disk-size SIZE    Disk size (default: $DISK_SIZE)
    --ssh-key PATH      SSH public key path (default: $SSH_KEY)
    --bridge BRIDGE     Network bridge (default: $BRIDGE_IF)
    --username USER     VM username (default: $USERNAME)
    --help, -h          Show this help

EXAMPLES:
    $0                                    # Create VM with defaults
    $0 --name myvm --memory 8192          # Custom name and memory
    $0 --disk-size 80G --vcpus 4          # Larger disk and more CPUs

PREREQUISITES:
    - KVM/QEMU installed and running
    - libvirt daemon running
    - virt-install package installed
    - SSH key pair generated
    - Network bridge configured (or use default virbr0)

EOF
}

# === Argument Parsing ===
while [[ $# -gt 0 ]]; do
    case $1 in
    --name)
        VM_NAME="$2"
        WORKDIR="/var/lib/libvirt/images/${VM_NAME}-vm"
        CLOUD_INIT_DIR="${WORKDIR}/cloud-init"
        BASE_IMG="${WORKDIR}/arch-latest.qcow2"
        DISK_IMG="${WORKDIR}/${VM_NAME}.qcow2"
        SEED_ISO="${CLOUD_INIT_DIR}/seed.iso"
        shift 2
        ;;
    --memory)
        RAM_MB="$2"
        shift 2
        ;;
    --vcpus)
        VCPUS="$2"
        shift 2
        ;;
    --disk-size)
        DISK_SIZE="$2"
        shift 2
        ;;
    --ssh-key)
        SSH_KEY="$2"
        shift 2
        ;;
    --bridge)
        BRIDGE_IF="$2"
        shift 2
        ;;
    --username)
        USERNAME="$2"
        shift 2
        ;;
    --help | -h)
        usage
        exit 0
        ;;
    *)
        log_error "Unknown option: $1"
        usage
        exit 1
        ;;
    esac
done

check_prerequisites() {
    log_info "Checking prerequisites..."

    # Check for sudo privileges
    if ! sudo -v; then
        log_error "This script requires sudo privileges to create files in system directories"
        exit 1
    fi

    # Determine QEMU user and group
    if getent passwd libvirt-qemu >/dev/null; then
        QEMU_USER="libvirt-qemu"
        QEMU_GROUP="libvirt-qemu"
    elif getent passwd qemu >/dev/null; then
        QEMU_USER="qemu"
        QEMU_GROUP="qemu"
    elif getent passwd libvirt >/dev/null; then
        QEMU_USER="libvirt"
        QEMU_GROUP="libvirt"
    else
        log_warn "Could not determine QEMU user, using 'root:kvm' as fallback"
        QEMU_USER="root"
        QEMU_GROUP="kvm"
    fi

    log_info "Using QEMU user/group: $QEMU_USER:$QEMU_GROUP"

    local missing_tools=()
    for tool in virsh virt-install qemu-img wget mkisofs; do
        if ! command -v "$tool" &>/dev/null; then
            missing_tools+=("$tool")
        fi
    done

    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Install with: sudo pacman -S qemu-desktop libvirt virt-install qemu-img wget cdrtools"
        exit 1
    fi

    # Check if libvirtd is running
    if ! systemctl is-active --quiet libvirtd; then
        log_error "libvirtd service is not running"
        log_error "Start with: sudo systemctl start libvirtd"
        exit 1
    fi

    # Check if user is in libvirt group
    if ! groups | grep -q libvirt; then
        log_warn "User not in libvirt group. You may need sudo for virsh commands"
        log_warn "Add user to group: sudo usermod -a -G libvirt \$USER"
    fi

    # Check SSH key
    if [[ ! -f "$SSH_KEY" ]]; then
        log_error "SSH public key not found at: $SSH_KEY"
        log_error "Generate with: ssh-keygen -t rsa -C '<EMAIL>'"
        exit 1
    fi

    # Check if VM already exists
    if virsh list --all | grep -q "$VM_NAME"; then
        log_error "VM '$VM_NAME' already exists"
        log_error "Remove with: virsh destroy $VM_NAME && virsh undefine $VM_NAME"
        exit 1
    fi

    # Check network bridge
    if ! ip link show "$BRIDGE_IF" &>/dev/null; then
        log_warn "Bridge '$BRIDGE_IF' not found, will use default libvirt network"
        BRIDGE_IF="default"
    fi

    log_success "Prerequisites check passed"
}

start_sudo_keepalive() {
    log_info "Starting sudo keepalive process..."
    sudo -v
    (while true; do
        sudo -v
        sleep 50
    done) &
    SUDO_KEEPALIVE_PID=$!
}

stop_sudo_keepalive() {
    if [ -n "$SUDO_KEEPALIVE_PID" ] && kill -0 "$SUDO_KEEPALIVE_PID" 2>/dev/null; then
        log_info "Stopping sudo keepalive process..."
        kill "$SUDO_KEEPALIVE_PID" 2>/dev/null
    fi
    SUDO_KEEPALIVE_PID=""
}

download_arch_image() {
    log_info "Preparing Arch Linux cloud image..."

    if [[ -f "$BASE_IMG" ]]; then
        log_info "Using existing Arch image: $BASE_IMG"
        return 0
    fi

    log_info "Downloading Arch Linux cloud image..."
    log_info "URL: $ARCH_IMG_URL"

    sudo mkdir -p "$WORKDIR"
    sudo chown "$QEMU_USER:$QEMU_GROUP" "$WORKDIR"
    sudo chmod 755 "$WORKDIR"

    if ! sudo wget -O "$BASE_IMG" "$ARCH_IMG_URL"; then
        log_error "Failed to download Arch cloud image"
        sudo rm -f "$BASE_IMG"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$BASE_IMG"
    sudo chmod 644 "$BASE_IMG"
    log_success "Arch cloud image downloaded: $BASE_IMG"
}

create_vm_disk() {
    log_info "Creating VM disk..."

    if ! sudo cp "$BASE_IMG" "$DISK_IMG"; then
        log_error "Failed to copy base image"
        exit 1
    fi

    if ! sudo qemu-img resize "$DISK_IMG" "$DISK_SIZE"; then
        log_error "Failed to resize VM disk"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$DISK_IMG"
    sudo chmod 644 "$DISK_IMG"
    log_success "VM disk created: $DISK_IMG ($DISK_SIZE)"
}

generate_cloud_init() {
    log_info "Generating cloud-init configuration..."

    sudo mkdir -p "$CLOUD_INIT_DIR"
    sudo chown "$QEMU_USER:$QEMU_GROUP" "$CLOUD_INIT_DIR"
    sudo chmod 755 "$CLOUD_INIT_DIR"

    local pub_key
    pub_key=$(cat "$SSH_KEY")

    # Generate user-data for Arch Linux
    sudo tee "${CLOUD_INIT_DIR}/user-data" >/dev/null <<EOF
#cloud-config
hostname: $VM_NAME
manage_etc_hosts: true

# User configuration
users:
  - name: $USERNAME
    groups: [wheel]
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    lock_passwd: false
    passwd: "$PASSWORD_HASH"
    ssh_authorized_keys:
      - $pub_key

# System configuration
package_update: true
package_upgrade: true

packages:
  - openssh
  - qemu-guest-agent
  - curl
  - wget
  - vim
  - htop
  - git
  - unzip
  - base-devel

# System setup
runcmd:
  # Enable and start SSH service
  - systemctl enable --now sshd

  # Enable and start qemu-guest-agent
  - systemctl enable --now qemu-guest-agent

  # Update system
  - pacman -Syu --noconfirm

  # Create a test marker
  - touch /home/<USER>/vm-setup-complete
  - chown $USERNAME:$USERNAME /home/<USER>/vm-setup-complete

  # Set up some useful aliases
  - echo 'alias ll="ls -la"' >> /home/<USER>/.bashrc
  - echo 'alias la="ls -la"' >> /home/<USER>/.bashrc
  - echo 'alias pac="sudo pacman"' >> /home/<USER>/.bashrc

  # Ensure proper permissions
  - chown -R $USERNAME:$USERNAME /home/<USER>

# Optional: Set timezone
timezone: UTC

# Write some useful files
write_files:
  - path: /etc/motd
    content: |
      Welcome to $VM_NAME Arch Linux VM!

      Arch Linux is ready to use.
      User: $USERNAME (passwordless sudo enabled)

      Quick commands:
        sudo pacman -Syu        # Update system
        sudo pacman -S <pkg>    # Install package
        pacman -Ss <term>       # Search packages

    append: false

# Final message
final_message: "VM $VM_NAME setup complete! Arch Linux is ready to use."
EOF

    # Generate meta-data
    sudo tee "${CLOUD_INIT_DIR}/meta-data" >/dev/null <<EOF
instance-id: ${VM_NAME}-$(date +%s)
local-hostname: $VM_NAME
EOF

    # Generate network-config
    sudo tee "${CLOUD_INIT_DIR}/network-config" >/dev/null <<EOF
version: 2
ethernets:
  enp1s0:
    dhcp4: true
EOF

    sudo chown -R "$QEMU_USER:$QEMU_GROUP" "$CLOUD_INIT_DIR"
    sudo chmod -R 644 "$CLOUD_INIT_DIR"/*
    sudo chmod 755 "$CLOUD_INIT_DIR"
    log_success "Cloud-init configuration generated"
}

create_cloud_init_iso() {
    log_info "Creating cloud-init ISO..."

    if ! sudo mkisofs -output "$SEED_ISO" -volid cidata -joliet -rock \
        "${CLOUD_INIT_DIR}/user-data" \
        "${CLOUD_INIT_DIR}/meta-data" \
        "${CLOUD_INIT_DIR}/network-config" 2>/dev/null; then
        log_error "Failed to create cloud-init ISO"
        exit 1
    fi

    sudo chown "$QEMU_USER:$QEMU_GROUP" "$SEED_ISO"
    sudo chmod 644 "$SEED_ISO"
    log_success "Cloud-init ISO created: $SEED_ISO"
}

launch_vm() {
    log_info "Launching VM '$VM_NAME'..."

    local network_config
    if [[ "$BRIDGE_IF" == "default" ]]; then
        network_config="network=default,model=virtio"
    else
        network_config="bridge=$BRIDGE_IF,model=virtio"
    fi

    # Use archlinux as os-variant (generic Linux fallback)
    local os_variant="archlinux"

    # Check if archlinux variant is available, fallback to generic
    if ! osinfo-query os | grep -q "archlinux"; then
        log_warn "OS variant 'archlinux' not found, using 'linux'"
        os_variant="linux"
    fi

    log_info "VM Configuration:"
    log_info "  Name: $VM_NAME"
    log_info "  Memory: ${RAM_MB}MB"
    log_info "  vCPUs: $VCPUS"
    log_info "  Disk: $DISK_IMG ($DISK_SIZE)"
    log_info "  Network: $network_config"
    log_info "  OS Variant: $os_variant"

    if ! virt-install \
        --connect qemu:///system \
        --name "$VM_NAME" \
        --memory "$RAM_MB" \
        --vcpus "$VCPUS" \
        --disk path="$DISK_IMG",format=qcow2,bus=virtio \
        --disk path="$SEED_ISO",device=cdrom \
        --os-variant "$os_variant" \
        --virt-type kvm \
        --graphics none \
        --network "$network_config" \
        --import \
        --noautoconsole; then
        log_error "Failed to create VM"
        exit 1
    fi

    log_success "VM '$VM_NAME' created successfully!"

    # Add VM to /etc/hosts for easy access
    log_info "Adding VM to /etc/hosts for name-based access..."
    sleep 5 # Give VM time to get IP
    if [[ -x "$HOME/.ilm/bin/vim-hosts" ]]; then
        "$HOME"/.ilm/bin/vim-hosts add "$VM_NAME" || log_warn "Could not add VM to /etc/hosts automatically"
    fi
}

show_completion_info() {
    log_success "=== VM Creation Complete ==="
    echo
    log_info "VM Details:"
    echo "  Name: $VM_NAME"
    echo "  Memory: ${RAM_MB}MB"
    echo "  vCPUs: $VCPUS"
    echo "  Disk: $DISK_SIZE"
    echo "  Username: $USERNAME"
    echo "  SSH Key: $SSH_KEY"
    echo
    log_info "Useful Commands:"
    echo "  Check VM status:    virsh list --all"
    echo "  Start VM:           virsh start $VM_NAME"
    echo "  Stop VM:            virsh shutdown $VM_NAME"
    echo "  Force stop VM:      virsh destroy $VM_NAME"
    echo "  Delete VM:          virsh undefine $VM_NAME"
    echo "  Console access:     virsh console $VM_NAME"
    echo "  Get VM IP:          virsh domifaddr $VM_NAME"
    echo
    log_info "SSH Access:"
    echo "  Wait 2-3 minutes for cloud-init to complete"
    echo "  Find VM IP:         virsh domifaddr $VM_NAME"
    echo "  SSH by name:        ssh $USERNAME@$VM_NAME"
    echo "  SSH by IP:          ssh $USERNAME@<VM_IP>"
    echo
    log_info "Arch Linux Commands (after SSH):"
    echo "  Update system:      sudo pacman -Syu"
    echo "  Install packages:   sudo pacman -S <package>"
    echo "  Search packages:    pacman -Ss <term>"
    echo "  List installed:     pacman -Q"
    echo "  AUR helper setup:   Install yay or paru for AUR access"
    echo
    log_warn "Note: Cloud-init setup takes 1-2 minutes. Check /home/<USER>/vm-setup-complete for completion."
}

main() {
    log_info "Starting Arch Linux VM creation..."
    log_info "VM Name: $VM_NAME"
    log_info "Working Directory: $WORKDIR"

    check_prerequisites
    start_sudo_keepalive
    download_arch_image
    create_vm_disk
    generate_cloud_init
    create_cloud_init_iso
    launch_vm
    show_completion_info

    log_success "All done! Your Arch Linux VM is ready."
}

main "$@"
